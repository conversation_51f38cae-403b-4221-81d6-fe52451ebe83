<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('action_record_items', function (Blueprint $table) {
            $table->id();
            $table->foreignId('action_record_id')->constrained();
            $table->text('action');
            $table->foreignId('team_id')->constrained();
            $table->date('deadline');
            $table->date('finished_at')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('action_record_items');
    }
};
