<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('action_records', function (Blueprint $table) {
            $table->id();
            $table->foreignId('created_by_operator_id')->constrained('operators');
            $table->string('created_by_operator_name');
            $table->foreignId('team_id')->constrained();
            $table->string('subject', 500);
            $table->text('description');
            $table->text('immediate_action');
            $table->text('consequences');
            $table->text('causes');
            $table->text('five_whys_description');
            $table->text('five_whys_infrastructure');
            $table->text('five_whys_personal');
            $table->text('five_whys_methods');
            $table->text('five_whys_materials');
            $table->boolean('valid_problem');
            $table->boolean('similar_action_records_exist');
            $table->boolean('send_to_client');
            $table->boolean('recurrent');
            $table->boolean('needs_to_update_risks_and_opportunities');
            $table->boolean('needs_to_change_qms');
            $table->dateTime('finished_at')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('action_records');
    }
};
