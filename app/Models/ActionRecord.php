<?php

namespace App\Models;

use App\Core\Module;
use App\Models\Concerns\ActionRecord\HandlesActionRecordRelationships;

/**
 * Action record model.
 *
 * @package App\Models
 *
 * @property  int $id
 * @property  int $created_by_operator_id
 * @property  string $created_by_operator_name
 * @property  int $team_id
 * @property  string $subject
 * @property  string $description
 * @property  string $immediate_action
 * @property  string $consequences
 * @property  string $causes
 * @property  string $five_whys_description
 * @property  string $five_whys_infrastructure
 * @property  string $five_whys_personal
 * @property  string $five_whys_methods
 * @property  string $five_whys_materials
 * @property  bool $valid_problem
 * @property  bool $similar_action_records_exist
 * @property  bool $send_to_client
 * @property  bool $recurrent
 * @property  bool $needs_to_update_risks_and_opportunities
 * @property  bool $needs_to_change_qms
 * @property  \Carbon\Carbon $finished_at
 * @property  \Carbon\Carbon $created_at
 * @property  \Carbon\Carbon $updated_at
 * @property  \Carbon\Carbon $deleted_at
 *
 * @property  \App\Models\Operator $creationOperator
 * @property  \App\Models\Team $team
 *
 * @property  \Illuminate\Support\Collection|\App\Models\ActionRecordItem[] $actionRecordItems
 * @property  \Illuminate\Support\Collection|\App\Models\ActionRecordOrigin[] $actionRecordOrigins
 */
class ActionRecord extends Model
{
    use HandlesActionRecordRelationships;

    public const MODULE = Module::QUALITY;
    public const RESOURCE_ROUTE = 'action_records';

    protected $fillable = [
        'created_by_operator_id',
        'created_by_operator_name',
        'team_id',
        'subject',
        'description',
        'immediate_action',
        'consequences',
        'causes',
        'five_whys_description',
        'five_whys_infrastructure',
        'five_whys_personal',
        'five_whys_methods',
        'five_whys_materials',
        'valid_problem',
        'similar_action_records_exist',
        'send_to_client',
        'recurrent',
        'needs_to_update_risks_and_opportunities',
        'needs_to_change_qms',
        'finished_at',
    ];

    protected $casts = [
        'valid_problem' => 'bool',
        'similar_action_records_exist' => 'bool',
        'send_to_client' => 'bool',
        'recurrent' => 'bool',
        'needs_to_update_risks_and_opportunities' => 'bool',
        'needs_to_change_qms' => 'bool',
    ];

    protected static function booted()
    {
        static::creating(function (self $actionRecord) {
            $actionRecord->fill([
                'created_by_operator_id' => auth()->user()->operator->id,
                'created_by_operator_name' => auth()->user()->operator->name,
            ]);
        });
    }
}
