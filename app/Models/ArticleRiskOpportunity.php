<?php

namespace App\Models;

use App\Models\Concerns\ArticleRiskOpportunity\HandlesArticleRiskOpportunityRelationships;

/**
 * Article risk opportunity model.
 *
 * @package App\Models
 *
 * @property  int $id
 * @property  int $article_id
 * @property  string $type
 * @property  string $harm_or_benefit
 * @property  string $description
 * @property  string $probability
 * @property  string $containment
 * @property  \Carbon\Carbon $created_at
 * @property  \Carbon\Carbon $updated_at
 *
 * @property  \App\Models\Article $article
 */
class ArticleRiskOpportunity extends Model
{
    use HandlesArticleRiskOpportunityRelationships;

    protected $fillable = [
        'article_id',
        'type',
        'harm_or_benefit',
        'description',
        'probability',
        'containment',
    ];
}
