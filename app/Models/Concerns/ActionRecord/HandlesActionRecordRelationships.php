<?php

namespace App\Models\Concerns\ActionRecord;

use App\Models\ActionRecordItem;
use App\Models\ActionRecordOrigin;
use App\Models\Operator;
use App\Models\Team;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

trait HandlesActionRecordRelationships
{
    public function creationOperator(): BelongsTo
    {
        return $this->belongsTo(Operator::class, 'created_by_operator_id');
    }

    public function team(): BelongsTo
    {
        return $this->belongsTo(Team::class);
    }

    public function actionRecordItems(): HasMany
    {
        return $this->hasMany(ActionRecordItem::class);
    }

    public function actionRecordOrigins(): HasMany
    {
        return $this->hasMany(ActionRecordOrigin::class);
    }
}
