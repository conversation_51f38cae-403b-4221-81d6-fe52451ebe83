<?php

namespace App\Models\Concerns\ActionRecordItem;

use App\Models\ActionRecord;
use App\Models\Team;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

trait HandlesActionRecordItemRelationships
{
    public function actionRecord(): BelongsTo
    {
        return $this->belongsTo(ActionRecord::class);
    }

    public function team(): BelongsTo
    {
        return $this->belongsTo(Team::class);
    }
}
