<?php

namespace App\Models\Concerns\Article;

use App\Models\ArticleArticleTag;
use App\Models\ArticleCategory;
use App\Models\ArticleRecord;
use App\Models\ArticleRevision;
use App\Models\ArticleRiskOpportunity;
use App\Models\Operator;
use App\Models\Team;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;

trait HandlesArticleRelationships
{
    public function articleCategory(): BelongsTo
    {
        return $this->belongsTo(ArticleCategory::class);
    }

    public function team(): BelongsTo
    {
        return $this->belongsTo(Team::class);
    }

    public function author(): BelongsTo
    {
        return $this->belongsTo(Operator::class, 'author_id');
    }

    public function approvalOperator(): BelongsTo
    {
        return $this->belongsTo(Operator::class, 'approval_operator_id');
    }

    public function articleArticleTags(): HasMany
    {
        return $this->hasMany(ArticleArticleTag::class);
    }

    public function articleRevisions(): HasMany
    {
        return $this->hasMany(ArticleRevision::class)->orderBy('sequence', 'desc');
    }

    public function articleRecords(): HasMany
    {
        return $this->hasMany(ArticleRecord::class);
    }

    public function articleRiskOpportunities(): HasMany
    {
        return $this->hasMany(ArticleRiskOpportunity::class);
    }

    public function latestRevision(): HasOne
    {
        return $this->hasOne(ArticleRevision::class)->latestOfMany('sequence');
    }

    public function latestApprovedRevision(): HasOne
    {
        return $this->hasOne(ArticleRevision::class)
            ->where('is_approved', true)
            ->latestOfMany('sequence');
    }
}
