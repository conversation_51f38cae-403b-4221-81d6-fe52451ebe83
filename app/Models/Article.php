<?php

namespace App\Models;

use App\Core\Module;
use App\Enums\ArticleStatusEnum;
use App\Models\Concerns\Article\HandlesArticleAttributes;
use App\Models\Concerns\Article\HandlesArticleRelationships;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Support\Str;

/**
 * Article model.
 *
 * @package App\Models
 *
 * @property  int $id
 * @property  int $article_category_id
 * @property  int $team_id
 * @property  int $author_id
 * @property  string $author_name
 * @property  string $title
 * @property  string $slug
 * @property  string $objective
 * @property  string $inputs_previous_articles
 * @property  string $outputs
 * @property  string $resources
 * @property  string $department_indicators
 * @property  string $target_type
 * @property  string $target_value
 * @property  string $linked_documents
 * @property  string $content
 * @property  string $status
 * @property  int $approval_operator_id
 * @property  bool $public
 * @property  \Carbon\Carbon $created_at
 * @property  \Carbon\Carbon $updated_at
 *
 * @property  string $status_badge
 *
 * @property  \App\Models\ArticleCategory $articleCategory
 * @property  \App\Models\Team $team
 * @property  \App\Models\Operator $author
 * @property  \App\Models\Operator $approvalOperator
 *
 * @property  \Illuminate\Support\Collection|\App\Models\ArticleArticleTag[] $articleArticleTags
 * @property  \Illuminate\Support\Collection|\App\Models\ArticleRevision[] $articleRevisions
 * @property  \Illuminate\Support\Collection|\App\Models\ArticleRecord[] $articleRecords
 * @property  \Illuminate\Support\Collection|\App\Models\ArticleRiskOpportunity[] $articleRiskOpportunities
 */
class Article extends Model
{
    use HandlesArticleAttributes;
    use HandlesArticleRelationships;
    use HasFactory;

    public const MODULE = Module::KNOWLEDGE_BASE;
    public const RESOURCE_ROUTE = 'articles';

    protected $fillable = [
        'article_category_id',
        'team_id',
        'author_id',
        'author_name',
        'title',
        'slug',
        'objective',
        'inputs_previous_articles',
        'outputs',
        'resources',
        'department_indicators',
        'target_type',
        'target_value',
        'linked_documents',
        'content',
        'status',
        'approval_operator_id',
        'public',
    ];

    protected $casts = [
        'public' => 'bool',
    ];

    protected static function booted()
    {
        static::creating(function (self $article): void {
            $article->fill([
                'team_id' => TeamOperator::query()
                    ->where('operator_id', auth()->user()->operator->id)
                    ->first()
                    ?->team_id,
                'author_id' => auth()->user()->operator->id,
                'author_name' => auth()->user()->operator->name,
                'slug' => Str::slug($article->title, language: 'pt'),
                'status' => ArticleStatusEnum::Pending->value,
            ]);
        });
    }
}
