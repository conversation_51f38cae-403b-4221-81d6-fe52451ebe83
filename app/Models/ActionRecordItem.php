<?php

namespace App\Models;

use App\Core\Module;
use App\Models\Concerns\ActionRecordItem\HandlesActionRecordItemRelationships;

/**
 * Action record item model.
 *
 * @package App\Models
 *
 * @property  int $id
 * @property  int $action_record_id
 * @property  string $action
 * @property  int $team_id
 * @property  \DateTime $deadline
 * @property  \DateTime $finished_at
 * @property  \Carbon\Carbon $created_at
 * @property  \Carbon\Carbon $updated_at
 *
 * @property  \App\Models\ActionRecord $actionRecord
 * @property  \App\Models\Team $team
 */
class ActionRecordItem extends Model
{
    use HandlesActionRecordItemRelationships;

    public const MODULE = Module::QUALITY;
    public const RESOURCE_ROUTE = 'action_record_items';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'action_record_id',
        'action',
        'team_id',
        'deadline',
        'finished_at',
    ];
}
