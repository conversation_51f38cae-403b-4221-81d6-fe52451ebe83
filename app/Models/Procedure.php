<?php

namespace App\Models;

use App\Core\Module;
use App\Models\Attributes\SetsNameAttribute;
use App\Models\Concerns\GetsForDropdown;
use App\Models\Concerns\Procedure\HandlesProcedureAttributes;
use App\Models\Concerns\Procedure\HandlesProcedureRelationships;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;

/**
 * Procedure model.
 *
 * @package App\Models
 *
 * @property  int $id
 * @property  string $erp_flex_id
 * @property  string $clm_online_id
 * @property  string $soc_id
 * @property  int $subcategory_id
 * @property  int $procedure_payment_method_id
 * @property  string $code
 * @property  string $name
 * @property  string $sales_name
 * @property  string $type
 * @property  string $objective
 * @property  string $scope
 * @property  float $default_price_table_amount
 * @property  float $default_provider_price_table_amount
 * @property  float $minimum_amount
 * @property  string $main_variant_code
 * @property  string $main_procedure_id
 * @property  bool $requires_service_order
 * @property  int $scheduling_sla
 * @property  int $visiting_sla
 * @property  int $checklist_receiving_sla
 * @property  int $inserting_sla
 * @property  int $attending_sla
 * @property  bool $enables_courtesy
 * @property  bool $participates_in_loss_ratio_calculation
 * @property  bool $active
 * @property  bool $characterizes_pcmso
 * @property  bool $characterizes_pgr
 * @property  bool $characterizes_aep
 * @property  bool $characterizes_noise_dosimetry
 * @property  bool $characterizes_gro
 * @property  bool $characterizes_ltcat
 * @property  bool $characterizes_ppp
 * @property  bool $characterizes_risk_map
 * @property  bool $characterizes_aet
 * @property  bool $characterizes_nr05_training
 * @property  bool $characterizes_ass_tec_med
 * @property  bool $characterizes_nr01_service_order
 * @property  bool $characterizes_sesmt
 * @property  bool $characterizes_unhealthiness_report
 * @property  bool $characterizes_nr06_training
 * @property  bool $characterizes_ass_tec_eng
 * @property  bool $characterizes_vibration_dosimetry
 * @property  bool $characterizes_chemical_evaluation
 * @property  bool $characterizes_cipa_onboarding
 * @property  bool $characterizes_eval_erg_workstation
 * @property  bool $characterizes_dangerousness_report
 * @property  bool $characterizes_sup_erg_home_office
 * @property  bool $characterizes_heat_dosimetry
 * @property  bool $characterizes_nr32_training
 * @property  bool $characterizes_nr35_training
 * @property  bool $characterizes_lectures
 * @property  bool $characterizes_nr_11_training
 * @property  bool $characterizes_art
 * @property  bool $characterizes_nr20_training
 * @property  bool $characterizes_ntep_challenge_report
 * @property  bool $characterizes_nr10_training
 * @property  bool $characterizes_inspection
 * @property  bool $characterizes_chemical_dosimetry
 * @property  bool $characterizes_tst_visitation
 * @property  bool $characterizes_group_blitz_erg
 * @property  bool $characterizes_individual_blitz_erg
 * @property  bool $characterizes_old_provider_report_digitization
 * @property  bool $characterizes_pgrss_training
 * @property  bool $characterizes_nr23_emergency_plan
 * @property  bool $characterizes_tst_visitation_cipa_follow_up
 * @property  bool $characterizes_workplace_gymnastics
 * @property  bool $characterizes_nr23_training
 * @property  bool $characterizes_first_aid_course
 * @property  bool $characterizes_psychosocial_factors_assessment
 * @property  bool $characterizes_service_order_module
 * @property  \Carbon\Carbon $created_at
 * @property  \Carbon\Carbon $updated_at
 *
 * @property  string $friendly_type
 * @property  string $friendly_default_price_table_amount
 * @property  string $friendly_default_provider_price_table_amount
 * @property  string $friendly_minimum_amount
 *
 * @property  \App\Models\Subcategory $subcategory
 * @property  \App\Models\Procedure $mainProcedure
 *
 * @property  \Illuminate\Support\Collection|\App\Models\Procedure[] $childProcedures
 * @property  \Illuminate\Support\Collection|\App\Models\ServiceAmountRangeTemplateItem[] $serviceAmountRangeTemplateItems
 * @property  \Illuminate\Support\Collection|\App\Models\ProviderProcedure[] $providerProcedures
 * @property  \Illuminate\Support\Collection|\App\Models\ProcedureCrmCityCoverage[] $procedureCrmCityCoverages
 */
class Procedure extends BaseModel
{
    use GetsForDropdown;
    use HandlesProcedureAttributes;
    use HandlesProcedureRelationships;
    use HasFactory;
    use SetsNameAttribute;

    public const MODULE = Module::MANAGEMENT;
    public const RESOURCE_ROUTE = 'procedures';

    public const TYPE_STAND_ALONE = 'stand-alone';
    public const TYPE_PER_CAPITA = 'per-capita';
    public const TYPE_AMOUNT_RANGE = 'amount-range';
    public const TYPE_EXAMS = 'exams';

    protected $fillable = [
        'erp_flex_id',
        'clm_online_id',
        'soc_id',
        'subcategory_id',
        'procedure_payment_method_id',
        'code',
        'name',
        'sales_name',
        'type',
        'objective',
        'scope',
        'default_price_table_amount',
        'default_provider_price_table_amount',
        'minimum_amount',
        'main_variant_code',
        'main_procedure_id',
        'requires_service_order',
        'scheduling_sla',
        'visiting_sla',
        'checklist_receiving_sla',
        'inserting_sla',
        'attending_sla',
        'enables_courtesy',
        'participates_in_loss_ratio_calculation',
        'active',
        'characterizes_pcmso',
        'characterizes_pgr',
        'characterizes_aep',
        'characterizes_noise_dosimetry',
        'characterizes_gro',
        'characterizes_ltcat',
        'characterizes_ppp',
        'characterizes_risk_map',
        'characterizes_aet',
        'characterizes_nr05_training',
        'characterizes_ass_tec_med',
        'characterizes_nr01_service_order',
        'characterizes_sesmt',
        'characterizes_unhealthiness_report',
        'characterizes_nr06_training',
        'characterizes_ass_tec_eng',
        'characterizes_vibration_dosimetry',
        'characterizes_chemical_evaluation',
        'characterizes_cipa_onboarding',
        'characterizes_eval_erg_workstation',
        'characterizes_dangerousness_report',
        'characterizes_sup_erg_home_office',
        'characterizes_heat_dosimetry',
        'characterizes_nr32_training',
        'characterizes_nr35_training',
        'characterizes_lectures',
        'characterizes_nr_11_training',
        'characterizes_art',
        'characterizes_nr20_training',
        'characterizes_ntep_challenge_report',
        'characterizes_nr10_training',
        'characterizes_inspection',
        'characterizes_chemical_dosimetry',
        'characterizes_tst_visitation',
        'characterizes_group_blitz_erg',
        'characterizes_individual_blitz_erg',
        'characterizes_old_provider_report_digitization',
        'characterizes_pgrss_training',
        'characterizes_nr23_emergency_plan',
        'characterizes_tst_visitation_cipa_follow_up',
        'characterizes_workplace_gymnastics',
        'characterizes_nr23_training',
        'characterizes_first_aid_course',
        'characterizes_psychosocial_factors_assessment',
        'characterizes_service_order_module',
    ];

    protected $casts = [
        'enables_courtesy' => 'bool',
        'participates_in_loss_ratio_calculation' => 'bool',
        'characterizes_pcmso' => 'bool',
        'characterizes_pgr' => 'bool',
        'characterizes_aep' => 'bool',
        'characterizes_noise_dosimetry' => 'bool',
        'characterizes_gro' => 'bool',
        'characterizes_ltcat' => 'bool',
        'characterizes_ppp' => 'bool',
        'characterizes_risk_map' => 'bool',
        'characterizes_aet' => 'bool',
        'characterizes_nr05_training' => 'bool',
        'characterizes_ass_tec_med' => 'bool',
        'characterizes_nr01_service_order' => 'bool',
        'characterizes_sesmt' => 'bool',
        'characterizes_unhealthiness_report' => 'bool',
        'characterizes_nr06_training' => 'bool',
        'characterizes_ass_tec_eng' => 'bool',
        'characterizes_vibration_dosimetry' => 'bool',
        'characterizes_chemical_evaluation' => 'bool',
        'characterizes_cipa_onboarding' => 'bool',
        'characterizes_eval_erg_workstation' => 'bool',
        'characterizes_dangerousness_report' => 'bool',
        'characterizes_sup_erg_home_office' => 'bool',
        'characterizes_heat_dosimetry' => 'bool',
        'characterizes_nr32_training' => 'bool',
        'characterizes_nr35_training' => 'bool',
        'characterizes_lectures' => 'bool',
        'characterizes_nr_11_training' => 'bool',
        'characterizes_art' => 'bool',
        'characterizes_nr20_training' => 'bool',
        'characterizes_ntep_challenge_report' => 'bool',
        'characterizes_nr10_training' => 'bool',
        'characterizes_inspection' => 'bool',
        'characterizes_chemical_dosimetry' => 'bool',
        'characterizes_tst_visitation' => 'bool',
        'characterizes_group_blitz_erg' => 'bool',
        'characterizes_individual_blitz_erg' => 'bool',
        'characterizes_old_provider_report_digitization' => 'bool',
        'characterizes_pgrss_training' => 'bool',
        'characterizes_nr23_emergency_plan' => 'bool',
        'characterizes_tst_visitation_cipa_follow_up' => 'bool',
        'characterizes_workplace_gymnastics' => 'bool',
        'characterizes_nr23_training' => 'bool',
        'characterizes_first_aid_course' => 'bool',
        'characterizes_psychosocial_factors_assessment' => 'bool',
        'characterizes_service_order_module' => 'bool',
    ];

    public static function getEquivalencesForDataTable(string $search = null): mixed
    {
        return self::query()
            ->with('mainProcedure:id,name')
            ->whereNotNull('main_procedure_id')
            ->whereNull('erp_flex_id')
            ->when(!is_null($search), function (Builder $query) use ($search) {
                return $query
                    ->where('name', 'like', "%$search%")
                    ->orWhereHas('mainProcedure', function (Builder $query) use ($search) {
                        return $query->where('name', 'like', "%$search%");
                    });
            });
    }

    public function scopeRenewableForServiceOrders(Builder $query): Builder
    {
        return $query
            ->where(function (Builder $query): Builder {
                return $query
                    ->where('characterizes_art', false)
                    ->where('characterizes_ass_tec_eng', false)
                    ->where('characterizes_ass_tec_med', false)
                    ->where('characterizes_psychosocial_factors_assessment', false)
                    ->where('characterizes_group_blitz_erg', false)
                    ->where('characterizes_individual_blitz_erg', false)
                    ->where('characterizes_old_provider_report_digitization', false)
                    ->where('characterizes_chemical_dosimetry', false)
                    ->where('characterizes_heat_dosimetry', false)
                    ->where('characterizes_noise_dosimetry', false)
                    ->where('characterizes_vibration_dosimetry', false)
                    ->where('characterizes_ntep_challenge_report', false)
                    ->where('characterizes_nr01_service_order', false)
                    ->where('characterizes_lectures', false)
                    ->where('characterizes_inspection', false)
                    ->where('characterizes_ppp', false)
                    ->where('characterizes_sup_erg_home_office', false)
                    ->where('characterizes_tst_visitation', false)
                    ->where('characterizes_tst_visitation_cipa_follow_up', false);
            });
    }

    public function verifyServiceOrderLiberationBypass(): bool
    {
        return $this->characterizes_pcmso
            || $this->characterizes_ppp
            || $this->characterizes_nr01_service_order;
    }

    public function isServiceOrderRenewable(): bool
    {
        return !$this->characterizes_art
            && !$this->characterizes_ass_tec_eng
            && !$this->characterizes_ass_tec_med
            && !$this->characterizes_psychosocial_factors_assessment
            && !$this->characterizes_group_blitz_erg
            && !$this->characterizes_individual_blitz_erg
            && !$this->characterizes_old_provider_report_digitization
            && !$this->characterizes_chemical_dosimetry
            && !$this->characterizes_heat_dosimetry
            && !$this->characterizes_noise_dosimetry
            && !$this->characterizes_vibration_dosimetry
            && !$this->characterizes_ntep_challenge_report
            && !$this->characterizes_nr01_service_order
            && !$this->characterizes_lectures
            && !$this->characterizes_inspection
            && !$this->characterizes_ppp
            && !$this->characterizes_sup_erg_home_office
            && !$this->characterizes_tst_visitation
            && !$this->characterizes_tst_visitation_cipa_follow_up;
    }
}
