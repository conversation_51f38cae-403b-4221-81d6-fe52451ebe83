<?php

namespace App\Models;

use App\Models\Concerns\ActionRecordOrigin\HandlesActionRecordOriginRelationships;

/**
 * Action record origin model.
 *
 * @package App\Models
 *
 * @property  int $id
 * @property  int $action_record_id
 * @property  string $name
 * @property  \Carbon\Carbon $created_at
 * @property  \Carbon\Carbon $updated_at
 *
 * @property  \App\Models\ActionRecord $actionRecord
 */
class ActionRecordOrigin extends Model
{
    use HandlesActionRecordOriginRelationships;

    protected $fillable = [
        'action_record_id',
        'name',
    ];
}
