<?php

namespace App\Actions\ServiceOrder;

use App\Models\Contract;
use App\Models\Procedure;
use App\Models\ServiceOrder;
use Illuminate\Http\RedirectResponse;
use Lorisleiva\Actions\Concerns\AsAction;
use Throwable;

class LiberateServiceOrder
{
    use AsAction;

    /**
     * Handle the action as a controller.
     *
     * @param  \App\Models\Contract $contract
     * @param  \App\Models\ServiceOrder $serviceOrder
     * @return \Illuminate\Http\RedirectResponse
     */
    public function asController(Contract $contract, ServiceOrder $serviceOrder): RedirectResponse
    {
        try {
            $this->handle($serviceOrder);

            return redirect_success(
                'contracts.edit',
                'A ordem de serviço foi liberada.',
                $contract->id
            );
        } catch (Throwable $th) {
            return redirect_error($th->getMessage());
        }
    }

    /**
     * Handle the action.
     *
     * @param  \App\Models\ServiceOrder $serviceOrder
     * @return \App\Models\ServiceOrder
     * @throws \Exception
     */
    public function handle(ServiceOrder $serviceOrder): ServiceOrder
    {
        try {
            if ($serviceOrder->procedure->verifyServiceOrderLiberationBypass()) {
                $updateData = [
                    'status' => ServiceOrder::STATUS_CHECKLIST_RECEIVED,
                    'started_at' => now(),
                    'scheduled_at' => now(),
                    'visited_at_from' => now(),
                    'visited_at_to' => now(),
                    'checklist_received_at' => now()
                ];
            } else {
                $updateData = [
                    'status' => ServiceOrder::STATUS_LIBERATED,
                    'started_at' => now()
                ];
            }

            $serviceOrder->update($updateData);

            return $serviceOrder;
        } catch (Throwable $th) {
            throw_error($th);
        }
    }
}
