<?php

namespace App\Enums;

enum ActionRecordOriginEnum: string
{
    case Process = 'process';
    case Client = 'client';
    case Provider = 'provider';
    case InternalAuditing = 'internal-auditing';
    case ExternalAuditing = 'external-auditing';
    case Reincidence = 'reincidence';

    public static function getTranslated(): array
    {
        return [
            self::Process->value => 'Processo',
            self::Client->value => 'Cliente',
            self::Provider->value => 'Credenciado',
            self::InternalAuditing->value => 'Auditoria interna',
            self::ExternalAuditing->value => 'Auditoria organismo',
            self::Reincidence->value => 'Reincidência',
        ];
    }
}
