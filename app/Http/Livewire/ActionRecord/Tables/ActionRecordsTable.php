<?php

namespace App\Http\Livewire\ActionRecord\Tables;

use Rappasoft\LaravelLivewireTables\DataTableComponent;
use Rappasoft\LaravelLivewireTables\Views\Column;
use App\Models\ActionRecord;

class ActionRecordsTable extends DataTableComponent
{
    protected $model = ActionRecord::class;

    public function configure(): void
    {
        $this->setPrimaryKey('id');
    }

    public function columns(): array
    {
        return [
            Column::make("Id", "id")
                ->sortable(),
            Column::make("Created by operator id", "created_by_operator_id")
                ->sortable(),
            Column::make("Created by operator name", "created_by_operator_name")
                ->sortable(),
            Column::make("Team id", "team_id")
                ->sortable(),
            Column::make("Subject", "subject")
                ->sortable(),
            Column::make("Description", "description")
                ->sortable(),
            Column::make("Immediate action", "immediate_action")
                ->sortable(),
            Column::make("Consequences", "consequences")
                ->sortable(),
            Column::make("Causes", "causes")
                ->sortable(),
            Column::make("Five whys description", "five_whys_description")
                ->sortable(),
            Column::make("Five whys infrastructure", "five_whys_infrastructure")
                ->sortable(),
            Column::make("Five whys personal", "five_whys_personal")
                ->sortable(),
            Column::make("Five whys methods", "five_whys_methods")
                ->sortable(),
            Column::make("Five whys materials", "five_whys_materials")
                ->sortable(),
            Column::make("Valid problem", "valid_problem")
                ->sortable(),
            Column::make("Similar action records exist", "similar_action_records_exist")
                ->sortable(),
            Column::make("Send to client", "send_to_client")
                ->sortable(),
            Column::make("Recurrent", "recurrent")
                ->sortable(),
            Column::make("Needs to update risks and opportunities", "needs_to_update_risks_and_opportunities")
                ->sortable(),
            Column::make("Needs to change qms", "needs_to_change_qms")
                ->sortable(),
            Column::make("Finished at", "finished_at")
                ->sortable(),
            Column::make("Created at", "created_at")
                ->sortable(),
            Column::make("Updated at", "updated_at")
                ->sortable(),
        ];
    }
}
